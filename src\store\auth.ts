import { create } from 'zustand';
import { auth } from '../lib/auth';

interface AuthState {
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  checkAuth: () => boolean;
}

export const useAuthStore = create<AuthState>()((set) => ({
  isAuthenticated: auth.isTokenValid(),
  login: async (username: string, password: string) => {
    if (username === 'admin' && password === 'password') {
      // In a real application, this would be an API call that returns a JWT
      const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiZXhwIjoxOTk5OTk5OTk5fQ.pW0VsW9qj-PB_5jvhvq3Vq7lhwR2AbhZiEHhfM2xq9o';
      auth.setToken(token);
      set({ isAuthenticated: true });
      return true;
    }
    return false;
  },
  logout: () => {
    auth.removeToken();
    set({ isAuthenticated: false });
  },
  checkAuth: () => {
    const isValid = auth.isTokenValid();
    set({ isAuthenticated: isValid });
    return isValid;
  },
}));