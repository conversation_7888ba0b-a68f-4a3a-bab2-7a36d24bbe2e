import { delay } from '../lib/utils';
import { auth } from '../lib/auth';

// Types for our mock data
interface NationalizeData {
  name: string;
  country: string;
  probability: string;
}

interface PopulationData {
  Year: string;
  Population: string;
}

interface TodoData {
  id: number;
  title: string;
  completed: boolean;
}

interface TradeData {
  price: string;
  quantity: string;
  time: string;
}

interface PokemonData {
  name: string;
  url: string;
}

// Helper functions to generate random data
const randomCountry = () => {
  const countries = ['US', 'GB', 'FR', 'DE', 'IT', 'ES', 'CA', 'AU', 'JP', 'BR'];
  return countries[Math.floor(Math.random() * countries.length)];
};

const randomName = () => {
  const names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
  return names[Math.floor(Math.random() * names.length)];
};

const randomTitle = () => {
  const actions = ['Complete', 'Review', 'Update', 'Fix', 'Implement', 'Test'];
  const items = ['documentation', 'bug report', 'feature request', 'pull request', 'unit tests'];
  const action = actions[Math.floor(Math.random() * actions.length)];
  const item = items[Math.floor(Math.random() * items.length)];
  return `${action} ${item}`;
};

const randomPrice = () => {
  const basePrice = 45000;
  const variance = 2000;
  return basePrice + (Math.random() - 0.5) * variance;
};

const generatePokemonData = () => {
  // List of 150 Pokemon names
  const pokemonNames = [
    'Bulbasaur', 'Ivysaur', 'Venusaur', 'Charmander', 'Charmeleon', 'Charizard', 'Squirtle', 
    'Wartortle', 'Blastoise', 'Caterpie', 'Metapod', 'Butterfree', 'Weedle', 'Kakuna', 'Beedrill', 
    'Pidgey', 'Pidgeotto', 'Pidgeot', 'Rattata', 'Raticate', 'Spearow', 'Fearow', 'Ekans', 'Arbok', 
    'Pikachu', 'Raichu', 'Sandshrew', 'Sandslash', 'Nidoran♀', 'Nidorina', 'Nidoqueen', 'Nidoran♂', 
    'Nidorino', 'Nidoking', 'Clefairy', 'Clefable', 'Vulpix', 'Ninetales', 'Jigglypuff', 'Wigglytuff', 
    'Zubat', 'Golbat', 'Oddish', 'Gloom', 'Vileplume', 'Paras', 'Parasect', 'Venonat', 'Venomoth', 
    'Diglett', 'Dugtrio', 'Meowth', 'Persian', 'Psyduck', 'Golduck', 'Mankey', 'Primeape', 'Growlithe', 
    'Arcanine', 'Poliwag', 'Poliwhirl', 'Poliwrath', 'Abra', 'Kadabra', 'Alakazam', 'Machop', 'Machoke', 
    'Machamp', 'Bellsprout', 'Weepinbell', 'Victreebel', 'Tentacool', 'Tentacruel', 'Geodude', 'Graveler', 
    'Golem', 'Ponyta', 'Rapidash', 'Slowpoke', 'Slowbro', 'Magnemite', 'Magneton', 'Farfetch\'d', 'Doduo', 
    'Dodrio', 'Seel', 'Dewgong', 'Grimer', 'Muk', 'Shellder', 'Cloyster', 'Gastly', 'Haunter', 'Gengar', 
    'Onix', 'Drowzee', 'Hypno', 'Krabby', 'Kingler', 'Voltorb', 'Electrode', 'Exeggcute', 'Exeggutor', 
    'Cubone', 'Marowak', 'Hitmonlee', 'Hitmonchan', 'Lickitung', 'Koffing', 'Weezing', 'Rhyhorn', 'Rhydon', 
    'Chansey', 'Tangela', 'Kangaskhan', 'Horsea', 'Seadra', 'Goldeen', 'Seaking', 'Staryu', 'Starmie', 
    'Mr. Mime', 'Scyther', 'Jynx', 'Electabuzz', 'Magmar', 'Pinsir', 'Tauros', 'Magikarp', 'Gyarados', 
    'Lapras', 'Ditto', 'Eevee', 'Vaporeon', 'Jolteon', 'Flareon', 'Porygon', 'Omanyte', 'Omastar', 
    'Kabuto', 'Kabutops', 'Aerodactyl', 'Snorlax', 'Articuno', 'Zapdos', 'Moltres', 'Dratini', 
    'Dragonair', 'Dragonite', 'Mewtwo', 'Mew'
  ];
  
  // Generate 1000 records by repeating the list with different variations
  return Array.from({ length: 1000 }, (_, index) => {
    const pokemonIndex = index % pokemonNames.length;
    const generation = Math.floor(index / pokemonNames.length) + 1;
    const name = `${pokemonNames[pokemonIndex]} (Gen ${generation})`;
    return {
      name,
      url: `https://pokeapi.co/api/v2/pokemon/${index + 1}/`
    };
  });
};

// Mock API endpoints with authentication headers
const makeAuthenticatedRequest = async <T>(dataFn: () => T): Promise<T> => {
  if (!auth.isTokenValid()) {
    throw new Error('Unauthorized');
  }
  await delay(500);
  return dataFn();
};

export const mockApi = {
  async getNationalizeData(): Promise<NationalizeData[]> {
    return makeAuthenticatedRequest(() => 
      Array.from({ length: 150 }, () => ({
        name: randomName(),
        country: randomCountry(),
        probability: (Math.random() * 100).toFixed(2) + '%',
      }))
    );
  },

  async getPopulationData(): Promise<PopulationData[]> {
    return makeAuthenticatedRequest(() =>
      Array.from({ length: 120 }, (_, i) => ({
        Year: (2023 - i).toString(),
        Population: (331_002_651 - i * 2_000_000).toLocaleString(),
      }))
    );
  },

  async getTodoData(): Promise<TodoData[]> {
    return makeAuthenticatedRequest(() =>
      Array.from({ length: 200 }, (_, i) => ({
        id: i + 1,
        title: randomTitle(),
        completed: Math.random() > 0.5,
      }))
    );
  },

  async getTradeData(): Promise<TradeData[]> {
    return makeAuthenticatedRequest(() =>
      Array.from({ length: 180 }, () => {
        const price = randomPrice();
        return {
          price: price.toLocaleString('en-US', { style: 'currency', currency: 'USD' }),
          quantity: (Math.random() * 2).toFixed(8),
          time: new Date().toLocaleString(),
        };
      })
    );
  },

  async getPokemonData(): Promise<PokemonData[]> {
    return makeAuthenticatedRequest(() => generatePokemonData());
  },
};