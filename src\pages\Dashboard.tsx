import { Link } from 'react-router-dom';
import { Database, Users, FileText, Bitcoin, Gamepad2 } from 'lucide-react';

const availableTables = [
  {
    id: 'nationalize',
    name: 'Name Nationalization',
    description: 'Predict the nationality of a person based on their name',
    icon: Users,
  },
  {
    id: 'population',
    name: 'US Population Data',
    description: 'Historical population data for the United States',
    icon: Database,
  },
  {
    id: 'todos',
    name: 'Todo List',
    description: 'Sample todo items and their completion status',
    icon: FileText,
  },
  {
    id: 'btctrades',
    name: 'BTC/USDT Trades',
    description: 'Real-time Bitcoin to USDT trading data',
    icon: Bitcoin,
  },
  {
    id: 'pokemon',
    name: 'Pokemon List',
    description: 'List of Pokemon with their details',
    icon: Gamepad2,
  },
];

export function Dashboard() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 p-8">
      <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1519681393784-d120267933ba')] bg-cover bg-center mix-blend-overlay opacity-20"></div>
      <div className="max-w-7xl mx-auto relative">
        <h1 className="text-4xl font-bold text-white mb-8 text-center">
          Available Tables
        </h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {availableTables.map((table) => {
            const Icon = table.icon;
            return (
              <Link
                key={table.id}
                to={`/table/${table.id}`}
                className="block group"
              >
                <div className="backdrop-blur-lg bg-white/20 dark:bg-gray-900/20 rounded-xl p-6 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-3 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-500">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <h2 className="text-xl font-semibold text-white">
                      {table.name}
                    </h2>
                  </div>
                  <p className="text-gray-200">
                    {table.description}
                  </p>
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
}