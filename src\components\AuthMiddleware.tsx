import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/auth';

interface AuthMiddlewareProps {
  children: React.ReactNode;
}

export function AuthMiddleware({ children }: AuthMiddlewareProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const { checkAuth } = useAuthStore();

  useEffect(() => {
    const isPublicRoute = location.pathname === '/login';
    const isAuthenticated = checkAuth();

    if (!isAuthenticated && !isPublicRoute) {
      navigate('/login', { replace: true });
    } else if (isAuthenticated && isPublicRoute) {
      navigate('/dashboard', { replace: true });
    }
  }, [location.pathname, navigate, checkAuth]);

  return <>{children}</>;
}