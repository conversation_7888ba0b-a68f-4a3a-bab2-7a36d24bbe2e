import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { createColumnHelper } from '@tanstack/react-table';
import { DataTable } from '../components/DataTable';
import { useAuthStore } from '../store/auth';
import { ArrowLeft } from 'lucide-react';
import { mockApi } from '../services/mockApi';
import toast from 'react-hot-toast';

const availableTables = {
  nationalize: {
    name: 'Name Nationalization',
    columns: ['name', 'country', 'probability'],
    getData: mockApi.getNationalizeData,
  },
  population: {
    name: 'US Population Data',
    columns: ['Year', 'Population'],
    getData: mockApi.getPopulationData,
  },
  todos: {
    name: 'Todo List',
    columns: ['id', 'title', 'completed'],
    getData: mockApi.getTodoData,
  },
  btctrades: {
    name: 'BTC/USDT Trades',
    columns: ['price', 'quantity', 'time'],
    getData: mockApi.getTradeData,
  },
  pokemon: {
    name: 'Pokemon List',
    columns: ['name', 'url'],
    getData: mockApi.getPokemonData,
  },
};

export function TableView() {
  const { tableId } = useParams<{ tableId: string }>();
  const navigate = useNavigate();
  const [data, setData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const token = useAuthStore((state) => state.token);

  const table = tableId ? availableTables[tableId as keyof typeof availableTables] : null;

  const fetchData = useCallback(async (showToast = true) => {
    if (!table) return;
    
    setIsLoading(true);
    try {
      const data = await table.getData();
      setData(data);
      if (showToast) {
        toast.success('Data refreshed successfully');
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to fetch data');
    } finally {
      setIsLoading(false);
    }
  }, [table]);

  useEffect(() => {
    if (!table) {
      navigate('/dashboard');
      return;
    }
    fetchData(false);
  }, [fetchData, navigate, table, tableId]);

  if (!table) return null;

  const columnHelper = createColumnHelper<any>();
  const columns = table.columns.map((col) => 
    columnHelper.accessor(col.toLowerCase(), {
      header: col,
      cell: (info) => info.getValue(),
    })
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 p-8">
      <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1519681393784-d120267933ba')] bg-cover bg-center mix-blend-overlay opacity-20"></div>
      <div className="max-w-7xl mx-auto relative">
        <div className="mb-8">
          <Link
            to="/dashboard"
            className="inline-flex items-center gap-2 text-white hover:text-gray-200 transition-colors mb-4 backdrop-blur-sm bg-white/10 px-4 py-2 rounded-lg"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back to Dashboard</span>
          </Link>
          <h1 className="text-4xl font-bold text-white">
            {table.name}
          </h1>
        </div>
        <DataTable
          data={data}
          columns={columns}
          onRefresh={() => fetchData(true)}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}