# DataTable Application With Authentication

This is a simple application that demonstrates how to create a data table with authentication. It uses React, TypeScript, and Vite.

## Features

- Login with username and password
- Authentication with JWT
- Data table with sorting, filtering, and pagination
- Export data to Excel
- Refresh data
- Dark mode
- Responsive design

## Getting Started

To get started with this project, follow these steps:

1. Clone the repository:

   ```bash
   git clone https://github.com/your-username/data-table-app.git
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Start the development server:

   ```bash
   npm run dev
   ```

## License

4. Open your browser and visit `http://localhost:3000` to see the application in action.
